// Debug utilities for authentication testing
export const clearAuthData = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  console.log('✅ Authentication data cleared from localStorage');
  console.log('🔄 Please refresh the page to see the changes');
};

export const checkAuthState = () => {
  const token = localStorage.getItem('token');
  const user = localStorage.getItem('user');
  
  console.log('🔍 Current Authentication State:');
  console.log('Token:', token ? `${token.substring(0, 20)}...` : 'None');
  console.log('User:', user ? JSON.parse(user) : 'None');
  
  return { token, user: user ? JSON.parse(user) : null };
};

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).clearAuthData = clearAuthData;
  (window as any).checkAuthState = checkAuthState;
  console.log('🛠️ Auth debug functions available:');
  console.log('- clearAuthData() - Clear all auth data');
  console.log('- checkAuthState() - Check current auth state');
}
