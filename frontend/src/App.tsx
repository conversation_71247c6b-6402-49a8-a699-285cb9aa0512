import React from 'react';
import { BrowserRouter, useLocation } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import AppRoutes from './routes/AppRoutes';
import { FloatingAssistantButton } from './components/AIAssistant';
import './App.css';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <ThemeProvider>
          <CssBaseline />
          <BrowserRouter>
            <AuthProvider>
              <AppRoutes />
              <ConditionalAssistant />
            </AuthProvider>
          </BrowserRouter>
        </ThemeProvider>
      </LocalizationProvider>
    </QueryClientProvider>
  );
}

// Separate component to conditionally render the FloatingAssistantButton
const ConditionalAssistant = () => {
  const location = useLocation();

  // Don't show the assistant on exam pages, landing page, or any auth pages
  const isExamPage = location.pathname.includes('/mcq/exam/');
  const isLandingPage = location.pathname === '/';
  const isAuthPage = location.pathname === '/login' ||
                     location.pathname === '/register' ||
                     location.pathname === '/debug-register' ||
                     location.pathname === '/register-debug';

  if (isExamPage || isLandingPage || isAuthPage) {
    return null;
  }

  return <FloatingAssistantButton />;
};

export default App;
