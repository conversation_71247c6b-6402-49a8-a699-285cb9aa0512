import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional
from pathlib import Path

from app.core.config import settings

logger = logging.getLogger(__name__)


def send_email(
    email_to: str,
    subject: str,
    html_content: str,
    text_content: Optional[str] = None,
) -> bool:
    """
    Send an email using SMTP
    """
    try:
        # Skip sending emails if SMTP is not configured
        if not settings.SMTP_USER or not settings.SMTP_PASSWORD:
            logger.warning("SMTP not configured, skipping email send")
            logger.info(f"Would send email to {email_to} with subject: {subject}")
            logger.info(f"HTML content: {html_content}")
            return True

        # Create message
        msg = MIMEMultipart("alternative")
        msg["Subject"] = subject
        msg["From"] = f"{settings.EMAILS_FROM_NAME} <{settings.EMAILS_FROM_EMAIL or settings.SMTP_USER}>"
        msg["To"] = email_to

        # Add text content
        if text_content:
            text_part = MIMEText(text_content, "plain")
            msg.attach(text_part)

        # Add HTML content
        html_part = MIMEText(html_content, "html")
        msg.attach(html_part)

        # Send email
        with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server:
            server.starttls()
            server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
            server.send_message(msg)

        logger.info(f"Email sent successfully to {email_to}")
        return True

    except Exception as e:
        logger.error(f"Failed to send email to {email_to}: {str(e)}")
        return False


def send_verification_email(email_to: str, verification_token: str, user_name: str) -> bool:
    """
    Send email verification email
    """
    verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
    
    subject = "Verify your CampusPQ account"
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 10px;
            }}
            .title {{
                font-size: 24px;
                color: #333;
                margin-bottom: 20px;
            }}
            .content {{
                font-size: 16px;
                margin-bottom: 30px;
            }}
            .button {{
                display: inline-block;
                background-color: #1976d2;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 20px 0;
            }}
            .button:hover {{
                background-color: #1565c0;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 14px;
                color: #666;
                text-align: center;
            }}
            .warning {{
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🎓 CampusPQ</div>
                <h1 class="title">Verify Your Email Address</h1>
            </div>
            
            <div class="content">
                <p>Hello {user_name},</p>
                
                <p>Thank you for registering with CampusPQ! To complete your account setup and start your learning journey, please verify your email address by clicking the button below:</p>
                
                <div style="text-align: center;">
                    <a href="{verification_url}" class="button">Verify Email Address</a>
                </div>
                
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                    {verification_url}
                </p>
                
                <div class="warning">
                    <strong>Important:</strong> This verification link will expire in 24 hours. If you don't verify your email within this time, you'll need to request a new verification email.
                </div>
                
                <p>Once verified, you'll be able to:</p>
                <ul>
                    <li>Access your personalized dashboard</li>
                    <li>Take practice exams and quizzes</li>
                    <li>Track your learning progress</li>
                    <li>Connect with tutors and peers</li>
                </ul>
                
                <p>If you didn't create an account with CampusPQ, please ignore this email.</p>
            </div>
            
            <div class="footer">
                <p>Best regards,<br>The CampusPQ Team</p>
                <p>Need help? Contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """
    
    text_content = f"""
    Hello {user_name},

    Thank you for registering with CampusPQ! To complete your account setup, please verify your email address by visiting this link:

    {verification_url}

    This verification link will expire in 24 hours.

    If you didn't create an account with CampusPQ, please ignore this email.

    Best regards,
    The CampusPQ Team
    """
    
    return send_email(email_to, subject, html_content, text_content)


def send_password_reset_email(email_to: str, reset_token: str, user_name: str) -> bool:
    """
    Send password reset email (for future use)
    """
    reset_url = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"
    
    subject = "Reset your CampusPQ password"
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Reset Your Password - CampusPQ</title>
    </head>
    <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2>Reset Your Password</h2>
        <p>Hello {user_name},</p>
        <p>You requested to reset your password for your CampusPQ account. Click the link below to reset your password:</p>
        <p><a href="{reset_url}" style="background-color: #1976d2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
        <p>If you didn't request this, please ignore this email.</p>
        <p>Best regards,<br>The CampusPQ Team</p>
    </body>
    </html>
    """
    
    text_content = f"""
    Hello {user_name},

    You requested to reset your password for your CampusPQ account. Visit this link to reset your password:

    {reset_url}

    If you didn't request this, please ignore this email.

    Best regards,
    The CampusPQ Team
    """
    
    return send_email(email_to, subject, html_content, text_content)
